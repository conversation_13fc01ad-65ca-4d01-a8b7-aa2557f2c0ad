{"name": "emoji-extension", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "node scripts/build.js dev", "watch": "node scripts/watch.js", "build": "node scripts/build.js build", "build:prod": "node scripts/build.js build:prod", "build:no-indexeddb": "node scripts/build.js build:no-indexeddb", "build:minimal": "node scripts/build.js build:minimal", "build:userscript": "node scripts/build.js build:userscript", "build:userscript:remote": "node scripts/build.js build:userscript remote", "build:userscript:min": "node scripts/build.js build:userscript:min", "build:debug": "node scripts/build.js build:debug", "generate:json": "node scripts/generate-json.js", "serve": "vite preview", "test": "playwright test", "test:debug": "playwright test --debug", "test:extension": "playwright test --config=playwright.extension.config.ts", "test:extension:debug": "playwright test --config=playwright.extension.config.ts --debug", "release": "bash ./scripts/release.sh", "pack:crx": "node ./scripts/pack-crx.js", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx", "lint:fix": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit"}, "dependencies": {"ant-design-vue": "^4.2.6", "eslint": "^9.35.0", "globals": "^16.4.0", "pinia": "^2.3.1", "vue": "^3.5.21"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@eslint/js": "^9.35.0", "@playwright/test": "^1.55.0", "@tailwindcss/typography": "^0.5.16", "@types/chrome": "^0.1.9", "@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "crx": "^5.0.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-vue": "^10.4.0", "jsonc-eslint-parser": "^1.4.1", "less": "^4.4.1", "playwright": "^1.55.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "rolldown-vite": "^7.1.9", "tailwindcss": "^3.4.17", "terser": "^5.44.0", "typescript": "^5.9.2", "unplugin-auto-import": "^0.17.8", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.6", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^3.0.7"}, "devDependenciesExtra": {}, "dependenciesMeta": {}, "devDependenciesAdditions": {}, "devDependencies2": {}, "peerDependenciesMeta": {}, "bundledDependencies": [], "overrides": {}, "engines": {}, "pnpm": {"overrides": {"vite": "npm:rolldown-vite@^7.1.9"}}}