# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-temporary-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build output
.nuxt
dist

# Nuxt.js generated files
.output

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# SvelteKit build output
.svelte-kit
build

# SvelteKit generated files
.svelte

# Rematch stores
.rematch

# Vite build output
dist
dist-ssr
*.js.map
*.css.map

# Optic changelog files
.optic

# Vitest coverage directory
coverage/
.vitest-coverage

# Microsoft VSCode data
.vscode

# JetBrains IDEs
.idea

# Eslint/Prettier/Stylelint etc.
.eslintcache
.prettierignore
.stylelintcache

# Release artifacts
releases/
*.zip

# Test files
test-*.html
playwright-report/
test-results/
chrome-extension-v3.crx
pack-key.pem
src/types/defaultEmojiGroups.ts
package-lock.json
deno.lock
components.d.ts
default.json
public/assets/defaultEmojiGroups.json

# Content-only local fallback (should not be shared)
src/content/data/default.local.ts
.eslint-output.json
