<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>表情管理器 - Emoji Extension Manager</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Provide a minimal safe dayjs stub to satisfy libraries that call dayjs.extend
         (Some CDN bundles can conflict; the stub avoids runtime errors while still
         allowing a full dayjs to be loaded later if desired) -->
    <script>
      ;(function () {
        try {
          if (!window.dayjs) {
            // minimal dayjs-like function
            function dayjs_stub(input) {
              return {
                toString() {
                  return input == null ? '' : String(input)
                }
              }
            }
            dayjs_stub.extend = function () {
              // No-op for plugins
            }
            // internal marker used by some builds
            dayjs_stub.$i = dayjs_stub.$i || {}
            window.dayjs = dayjs_stub
            console.debug('Inserted dayjs stub')
          }
        } catch (e) {
          console.warn('Failed to install dayjs stub', e)
        }
      })()
    </script>
    <!-- Use a different CDN for ant-design-vue to avoid potential unpkg issues -->
    <script src="https://cdn.jsdelivr.net/npm/ant-design-vue@4/dist/antd.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/ant-design-vue@4/dist/reset.css" rel="stylesheet" />
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
    <style>
      .emoji-grid {
        display: grid;
        gap: 8px;
        padding: 16px;
      }
      .emoji-item {
        width: 48px;
        height: 48px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        border: 2px solid transparent;
      }
      .emoji-item:hover {
        border-color: #1890ff;
        transform: scale(1.1);
      }
      .group-header {
        background: #f5f5f5;
        padding: 12px;
        margin: 8px 0;
        border-radius: 6px;
        border-left: 4px solid #1890ff;
      }
      .management-section {
        background: white;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <div id="app">
      <a-layout style="min-height: 100vh">
        <!-- Header -->
        <a-layout-header style="background: #001529; padding: 0">
          <div
            style="
              color: white;
              padding: 0 24px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <h1 style="color: white; margin: 0; font-size: 18px">🐈‍⬛ 表情管理器</h1>
            <div>
              <a-button @click="exportData" style="margin-right: 8px">导出数据</a-button>
              <a-button @click="openImportModal" type="primary">导入数据</a-button>

              <!-- WebSocket controls -->
              <div style="display: inline-block; margin-left: 16px; vertical-align: middle">
                <input
                  v-model="wsUrl"
                  placeholder="ws://localhost:8765"
                  style="width: 220px; margin-right: 8px"
                />
                <a-button
                  size="small"
                  @click="connectWebSocket"
                  :disabled="wsConnected"
                  style="margin-right: 6px"
                >
                  连接 WS
                </a-button>
                <a-button
                  size="small"
                  type="default"
                  @click="disconnectWebSocket"
                  :disabled="!wsConnected"
                >
                  断开 WS
                </a-button>
                <span style="margin-left: 8px; color: #999">
                  状态:
                  <strong>{{ wsConnected ? '已连接' : '未连接' }}</strong>
                </span>
              </div>
            </div>
          </div>
        </a-layout-header>

        <a-layout>
          <!-- Sidebar -->
          <a-layout-sider width="200" style="background: #fff">
            <a-menu
              v-model:selected-keys="selectedKeys"
              mode="inline"
              style="height: 100%; border-right: 0"
            >
              <a-menu-item key="groups" @click="activeTab = 'groups'">
                <span>📁 分组管理</span>
              </a-menu-item>
              <a-menu-item key="settings" @click="activeTab = 'settings'">
                <span>⚙️ 设置</span>
              </a-menu-item>
              <a-menu-item key="favorites" @click="activeTab = 'favorites'">
                <span>⭐ 收藏夹</span>
              </a-menu-item>
              <a-menu-item key="export" @click="activeTab = 'export'">
                <span>📤 导出/同步</span>
              </a-menu-item>
            </a-menu>
          </a-layout-sider>

          <!-- Content -->
          <a-layout-content style="margin: 24px 16px; padding: 24px; background: #f0f2f5">
            <!-- Groups Management -->
            <div v-if="activeTab === 'groups'">
              <div class="management-section">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;
                  "
                >
                  <h2>表情分组管理</h2>
                  <a-button type="primary" @click="openCreateGroupModal">创建分组</a-button>
                </div>

                <!-- Groups List -->
                <a-list :data-source="emojiGroups" item-layout="vertical">
                  <template #renderItem="{ item: group }">
                    <a-list-item>
                      <template #actions>
                        <a @click="editGroup(group)">编辑</a>
                        <a @click="deleteGroup(group.id)">删除</a>
                        <a @click="openAddEmojiModal(group.id)">添加表情</a>
                      </template>

                      <div class="group-header">
                        <h3 style="margin: 0; display: flex; align-items: center">
                          <span style="margin-right: 8px; font-size: 18px">{{ group.icon }}</span>
                          {{ group.name }}
                          <a-tag style="margin-left: 8px">
                            {{ group.emojis?.length || 0 }} 个表情
                          </a-tag>
                        </h3>
                      </div>

                      <!-- Emojis Grid -->
                      <div
                        class="emoji-grid"
                        :style="{gridTemplateColumns: `repeat(${settings.gridColumns}, 1fr)`}"
                      >
                        <img
                          v-for="emoji in group.emojis"
                          :key="emoji.packet || emoji.name"
                          :src="emoji.url"
                          :alt="emoji.name"
                          :title="emoji.name"
                          class="emoji-item"
                          @click="editEmoji(emoji, group.id)"
                          @error="handleImageError"
                        />
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>

            <!-- Settings -->
            <div v-if="activeTab === 'settings'" class="management-section">
              <h2>全局设置</h2>

              <a-form layout="vertical">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="图片缩放比例">
                      <a-slider
                        v-model:value="settings.imageScale"
                        :min="5"
                        :max="150"
                        :step="1"
                        :marks="{30: '30%', 50: '50%', 100: '100%'}"
                      />
                      <div>当前: {{ settings.imageScale }}%</div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="网格列数">
                      <a-slider
                        v-model:value="settings.gridColumns"
                        :min="2"
                        :max="8"
                        :step="1"
                        :marks="{2: '2', 4: '4', 6: '6', 8: '8'}"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-form-item label="输出格式">
                  <a-radio-group v-model:value="settings.outputFormat">
                    <a-radio value="markdown">Markdown</a-radio>
                    <a-radio value="html">HTML</a-radio>
                  </a-radio-group>
                </a-form-item>

                <a-form-item>
                  <a-checkbox v-model:checked="settings.showSearchBar">显示搜索栏</a-checkbox>
                </a-form-item>

                <a-form-item>
                  <a-checkbox v-model:checked="settings.forceMobileMode">强制移动端模式</a-checkbox>
                </a-form-item>

                <a-form-item>
                  <a-button type="primary" @click="saveSettings">保存设置</a-button>
                </a-form-item>
              </a-form>
            </div>

            <!-- Favorites -->
            <div v-if="activeTab === 'favorites'" class="management-section">
              <h2>收藏夹</h2>
              <div
                v-if="favorites.length === 0"
                style="text-align: center; color: #999; padding: 40px"
              >
                还没有收藏的表情
              </div>
              <div
                v-else
                class="emoji-grid"
                :style="{gridTemplateColumns: `repeat(${settings.gridColumns}, 1fr)`}"
              >
                <img
                  v-for="emoji in favorites"
                  :key="emoji.id"
                  :src="emoji.url"
                  :alt="emoji.name"
                  :title="emoji.name"
                  class="emoji-item"
                  @click="removeFromFavorites(emoji.id)"
                />
              </div>
            </div>

            <!-- Export/Sync -->
            <div v-if="activeTab === 'export'" class="management-section">
              <h2>数据导出与同步</h2>

              <a-space direction="vertical" style="width: 100%">
                <div>
                  <h3>用户脚本同步</h3>
                  <p>将当前数据同步到用户脚本的localStorage中</p>
                  <a-button type="primary" @click="syncToUserscript">同步到用户脚本</a-button>
                </div>

                <a-divider />

                <div>
                  <h3>数据导出</h3>
                  <p>导出完整的表情数据以便备份或分享</p>
                  <a-space>
                    <a-button @click="exportData">导出 JSON</a-button>
                    <a-button @click="exportForUserscript">导出用户脚本数据</a-button>
                  </a-space>
                </div>

                <a-divider />

                <div>
                  <h3>数据导入</h3>
                  <p>从备份文件或其他来源导入表情数据</p>
                  <a-button @click="openImportModal">导入数据</a-button>
                </div>
              </a-space>
            </div>
          </a-layout-content>
        </a-layout>
      </a-layout>

      <!-- Create Group Modal -->
      <a-modal v-model:open="openCreateGroupModalFlag" title="创建分组" @ok="createGroup">
        <a-form layout="vertical">
          <a-form-item label="分组名称" required>
            <a-input v-model:value="newGroup.name" placeholder="输入分组名称" />
          </a-form-item>
          <a-form-item label="分组图标">
            <a-input v-model:value="newGroup.icon" placeholder="输入图标或emoji" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- Edit Group Modal -->
      <a-modal v-model:open="showEditGroupModal" title="编辑分组" @ok="updateGroup">
        <a-form layout="vertical">
          <a-form-item label="分组名称" required>
            <a-input v-model:value="editingGroup.name" placeholder="输入分组名称" />
          </a-form-item>
          <a-form-item label="分组图标">
            <a-input v-model:value="editingGroup.icon" placeholder="输入图标或emoji" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- Add Emoji Modal -->
      <a-modal v-model:open="openAddEmojiModalFlag" title="添加表情" @ok="addEmoji">
        <a-form layout="vertical">
          <a-form-item label="表情名称" required>
            <a-input v-model:value="newEmoji.name" placeholder="输入表情名称" />
          </a-form-item>
          <a-form-item label="图片URL" required>
            <a-input v-model:value="newEmoji.url" placeholder="输入图片链接" />
          </a-form-item>
          <a-form-item v-if="newEmoji.url">
            <img :src="newEmoji.url" style="max-width: 100px; max-height: 100px" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- Import Modal -->
      <a-modal v-model:open="openImportModalFlag" title="导入数据" @ok="performImport">
        <a-form layout="vertical">
          <a-form-item label="数据格式">
            <a-radio-group v-model:value="importFormat">
              <a-radio value="json">JSON 格式</a-radio>
              <a-radio value="userscript">用户脚本格式</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="数据内容">
            <a-textarea v-model:value="importData" :rows="10" placeholder="粘贴要导入的数据" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>

    <script>
      const { createApp } = Vue
      // Safely resolve the global variable that the UMD build of ant-design-vue exposes.
      // Different CDN/builds may expose different globals, so try several common names.
      const resolvedAntd =
        window.antd ||
        window['ant-design-vue'] ||
        window.Antd ||
        window.antdv ||
        window.antDesignVue
      if (!resolvedAntd) {
        // Keep a helpful console error for debugging if the CDN changed or failed to load.
        console.error(
          'Ant Design Vue (antd) is not available on window. UI components may fail to initialize.'
        )
      }
      console.debug(
        'resolvedAntd (debug):',
        resolvedAntd,
        'window.keys sample:',
        Object.keys(window).slice(0, 50)
      )

      // Provide minimal fallback stubs to avoid Vue complaining about unresolved components
      const safeFallback = {
        // Basic functional stubs that render their children as plain elements
        Layout: {
          render() {
            return null
          }
        },
        LayoutHeader: {
          render() {
            return null
          }
        },
        LayoutSider: {
          render() {
            return null
          }
        },
        LayoutContent: {
          render() {
            return null
          }
        },
        Menu: {
          render() {
            return null
          }
        },
        MenuItem: {
          render() {
            return null
          }
        },
        Button: {
          render() {
            return null
          }
        },
        List: {
          render() {
            return null
          }
        },
        ListItem: {
          render() {
            return null
          }
        },
        Tag: {
          render() {
            return null
          }
        },
        Form: {
          render() {
            return null
          }
        },
        FormItem: {
          render() {
            return null
          }
        },
        Input: {
          render() {
            return null
          }
        },
        Slider: {
          render() {
            return null
          }
        },
        RadioGroup: {
          render() {
            return null
          }
        },
        Radio: {
          render() {
            return null
          }
        },
        Checkbox: {
          render() {
            return null
          }
        },
        Space: {
          render() {
            return null
          }
        },
        Divider: {
          render() {
            return null
          }
        },
        Modal: {
          render() {
            return null
          }
        },
        Textarea: {
          render() {
            return null
          }
        },
        Row: {
          render() {
            return null
          }
        },
        Col: {
          render() {
            return null
          }
        },
        message: {
          success: msg => console.log('[message.success]', msg),
          error: msg => console.error('[message.error]', msg),
          info: msg => console.info('[message.info]', msg)
        }
      }

      const {
        Layout,
        LayoutHeader,
        LayoutSider,
        LayoutContent,
        Menu,
        MenuItem,
        Button,
        List,
        ListItem,
        Tag,
        Form,
        FormItem,
        Input,
        Slider,
        RadioGroup,
        Radio,
        Checkbox,
        Space,
        Divider,
        Modal,
        Textarea,
        Row,
        Col,
        message
      } = resolvedAntd || safeFallback

      createApp({
        components: {
          ALayout: Layout,
          ALayoutHeader: LayoutHeader,
          ALayoutSider: LayoutSider,
          ALayoutContent: LayoutContent,
          AMenu: Menu,
          AMenuItem: MenuItem,
          AButton: Button,
          AList: List,
          AListItem: ListItem,
          ATag: Tag,
          AForm: Form,
          AFormItem: FormItem,
          AInput: Input,
          ASlider: Slider,
          ARadioGroup: RadioGroup,
          ARadio: Radio,
          ACheckbox: Checkbox,
          ASpace: Space,
          ADivider: Divider,
          AModal: Modal,
          ATextarea: Textarea,
          ARow: Row,
          ACol: Col
        },
        data() {
          return {
            activeTab: 'groups',
            selectedKeys: ['groups'],
            emojiGroups: [],
            settings: {
              imageScale: 30,
              gridColumns: 4,
              outputFormat: 'markdown',
              forceMobileMode: false,
              defaultGroup: 'nachoneko',
              showSearchBar: true
            },
            favorites: [],
            // modal flags renamed to avoid collision with method names
            openCreateGroupModalFlag: false,
            showEditGroupModal: false,
            openAddEmojiModalFlag: false,
            openImportModalFlag: false,
            newGroup: { name: '', icon: '📁' },
            editingGroup: { id: '', name: '', icon: '' },
            newEmoji: { name: '', url: '', groupId: '' },
            importData: '',
            importFormat: 'json',
            // WebSocket state for userscript integration
            wsUrl: 'ws://localhost:8765',
            wsConnected: false,
            wsInstance: null
          }
        },
        mounted() {
          this.loadData()
          // Broadcast presence for userscripts to auto-discover manager and WS URL
          try {
            if (typeof BroadcastChannel !== 'undefined') {
              this.__bc = new BroadcastChannel('emoji-manager-channel')
              const sendPresence = () => {
                try {
                  this.__bc.postMessage({
                    type: 'manager:presence',
                    wsUrl: this.wsUrl,
                    wsConnected: this.wsConnected
                  })
                } catch (e) {
                  // ignore
                }
              }
              sendPresence()
              this.__bcInterval = setInterval(sendPresence, 5000)

              // Cleanup on unload
              window.addEventListener('beforeunload', () => {
                try {
                  clearInterval(this.__bcInterval)
                  this.__bc.close()
                } catch (e) {}
              })
            }
          } catch (e) {
            // ignore if BroadcastChannel unavailable
          }
        },
        methods: {
          loadData() {
            // Load from localStorage
            try {
              const groups = localStorage.getItem('emoji_extension_manager_groups')
              if (groups) {
                this.emojiGroups = JSON.parse(groups)
              } else {
                this.emojiGroups = this.getDefaultGroups()
              }

              const settings = localStorage.getItem('emoji_extension_manager_settings')
              if (settings) {
                this.settings = { ...this.settings, ...JSON.parse(settings) }
              }

              const favorites = localStorage.getItem('emoji_extension_manager_favorites')
              if (favorites) {
                this.favorites = JSON.parse(favorites)
              }
            } catch (error) {
              console.error('Failed to load data:', error)
              message.error('加载数据失败')
            }
          },

          saveData() {
            try {
              localStorage.setItem(
                'emoji_extension_manager_groups',
                JSON.stringify(this.emojiGroups)
              )
              localStorage.setItem(
                'emoji_extension_manager_settings',
                JSON.stringify(this.settings)
              )
              localStorage.setItem(
                'emoji_extension_manager_favorites',
                JSON.stringify(this.favorites)
              )
            } catch (error) {
              console.error('Failed to save data:', error)
              message.error('保存数据失败')
            }
          },

          getDefaultGroups() {
            return [
              {
                id: 'default',
                name: '默认表情',
                icon: '😀',
                order: 0,
                emojis: [
                  {
                    packet: 1,
                    name: '瞌睡',
                    url: 'https://linux.do/uploads/default/optimized/4X/5/9/f/59ffbc2c53dd2a07dc30d4368bd5c9e01ca57d80_2_490x500.jpeg'
                  },
                  {
                    packet: 2,
                    name: '哭泣',
                    url: 'https://linux.do/uploads/default/optimized/4X/5/d/9/5d932c05a642396335f632a370bd8d45463cf2e2_2_503x500.jpeg'
                  },
                  {
                    packet: 3,
                    name: '疑问',
                    url: 'https://linux.do/uploads/default/optimized/4X/f/a/a/faa5afe1749312bc4a326feff0eca6fb39355300_2_518x499.jpeg'
                  }
                ]
              }
            ]
          },

          openCreateGroupModal() {
            this.openCreateGroupModalFlag = true
          },

          openImportModal() {
            this.openImportModalFlag = true
          },

          createGroup() {
            if (!this.newGroup.name.trim()) {
              message.error('请输入分组名称')
              return
            }

            const newGroup = {
              id: Date.now().toString(),
              name: this.newGroup.name.trim(),
              icon: this.newGroup.icon || '📁',
              order: this.emojiGroups.length,
              emojis: []
            }

            this.emojiGroups.push(newGroup)
            this.saveData()
            this.showCreateGroupModal = false
            this.newGroup = { name: '', icon: '📁' }
            message.success('分组创建成功')
          },

          editGroup(group) {
            this.editingGroup = { ...group }
            this.showEditGroupModal = true
          },

          updateGroup() {
            const index = this.emojiGroups.findIndex(g => g.id === this.editingGroup.id)
            if (index !== -1) {
              this.emojiGroups[index] = { ...this.emojiGroups[index], ...this.editingGroup }
              this.saveData()
              this.showEditGroupModal = false
              message.success('分组更新成功')
            }
          },

          deleteGroup(groupId) {
            const index = this.emojiGroups.findIndex(g => g.id === groupId)
            if (index !== -1) {
              ;(async () => {
                try {
                  const mod = await import('./src/utils/confirmService.js')
                  const ok = await mod.requestConfirmation('确定要删除这个分组吗？')
                  if (ok) {
                    this.emojiGroups.splice(index, 1)
                    this.saveData()
                    message.success('分组删除成功')
                  }
                } catch (e) {
                  if (window.confirm('确定要删除这个分组吗？')) {
                    this.emojiGroups.splice(index, 1)
                    this.saveData()
                    message.success('分组删除成功')
                  }
                }
              })()
            }
          },

          openAddEmojiModal(groupId) {
            this.newEmoji.groupId = groupId
            this.openAddEmojiModalFlag = true
          },

          addEmoji() {
            if (!this.newEmoji.name.trim() || !this.newEmoji.url.trim()) {
              message.error('请填写表情名称和URL')
              return
            }

            const group = this.emojiGroups.find(g => g.id === this.newEmoji.groupId)
            if (group) {
              const emoji = {
                packet: Date.now(),
                name: this.newEmoji.name.trim(),
                url: this.newEmoji.url.trim()
              }

              group.emojis.push(emoji)
              this.saveData()
              this.showAddEmojiModal = false
              this.newEmoji = { name: '', url: '', groupId: '' }
              message.success('表情添加成功')
            }
          },

          editEmoji(emoji, groupId) {
            // TODO: Implement emoji editing
            console.log('Edit emoji:', emoji, groupId)
          },

          removeFromFavorites(emojiId) {
            const index = this.favorites.findIndex(f => f.id === emojiId)
            if (index !== -1) {
              this.favorites.splice(index, 1)
              this.saveData()
              message.success('已从收藏夹移除')
            }
          },

          saveSettings() {
            this.saveData()
            message.success('设置保存成功')
          },

          exportData() {
            const safeFallback = (function () {
              // helper to create a component that renders its default slot inside a tag
              function slotWrapper(tag = 'div') {
                return {
                  inheritAttrs: false,
                  render() {
                    const children = this.$slots && this.$slots.default ? this.$slots.default() : []
                    return Vue.h(tag, this.$attrs || {}, children)
                  }
                }
              }

              const Button = {
                inheritAttrs: false,
                render() {
                  const children = this.$slots && this.$slots.default ? this.$slots.default() : []
                  return Vue.h(
                    'button',
                    Object.assign({}, this.$attrs, {
                      onClick: e => {
                        this.$emit && this.$emit('click', e)
                      }
                    }),
                    children
                  )
                }
              }

              const Input = {
                inheritAttrs: false,
                props: ['value', 'modelValue'],
                render() {
                  const value = this.modelValue !== undefined ? this.modelValue : this.value
                  return Vue.h(
                    'input',
                    Object.assign({}, this.$attrs, {
                      value: value,
                      onInput: e => {
                        const v = e.target.value
                        this.$emit && this.$emit('update:modelValue', v)
                        this.$emit && this.$emit('update:value', v)
                      }
                    })
                  )
                }
              }

              const Modal = {
                inheritAttrs: false,
                props: ['open'],
                render() {
                  if (!this.open) return null
                  const body = this.$slots && this.$slots.default ? this.$slots.default() : []
                  const footer = Vue.h('div', { style: { marginTop: '12px' } }, [
                    Vue.h(
                      'button',
                      {
                        onClick: () => {
                          this.$emit && this.$emit('ok')
                          this.$emit && this.$emit('update:open', false)
                        }
                      },
                      'OK'
                    )
                  ])
                  return Vue.h(
                    'div',
                    {
                      class: 'modal-fallback',
                      style: { border: '1px solid #ddd', padding: '12px', background: '#fff' }
                    },
                    [body, footer]
                  )
                }
              }

              return {
                Layout: slotWrapper('div'),
                LayoutHeader: slotWrapper('header'),
                LayoutSider: slotWrapper('aside'),
                LayoutContent: slotWrapper('main'),
                Menu: slotWrapper('div'),
                MenuItem: slotWrapper('div'),
                Button: Button,
                List: slotWrapper('div'),
                ListItem: slotWrapper('div'),
                Tag: slotWrapper('span'),
                Form: slotWrapper('form'),
                FormItem: slotWrapper('div'),
                Input: Input,
                Slider: slotWrapper('input'),
                RadioGroup: slotWrapper('div'),
                Radio: slotWrapper('input'),
                Checkbox: slotWrapper('input'),
                Space: slotWrapper('div'),
                Divider: slotWrapper('hr'),
                Modal: Modal,
                Textarea: slotWrapper('textarea'),
                Row: slotWrapper('div'),
                Col: slotWrapper('div'),
                message: {
                  success: msg => console.log('[message.success]', msg),
                  error: msg => console.error('[message.error]', msg),
                  info: msg => console.info('[message.info]', msg)
                }
              }
            })()
            message.success('用户脚本数据导出成功')
          },

          exportForUserscript() {
            try {
              const payload = {
                emojiGroups: this.emojiGroups,
                settings: this.settings,
                favorites: this.favorites
              }
              const json = JSON.stringify(payload, null, 2)

              // Try to copy to clipboard (convenience for userscript authors)
              if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard
                  .writeText(json)
                  .then(() => {
                    message.success('已复制用户脚本格式到剪贴板')
                  })
                  .catch(() => {
                    message.info('复制到剪贴板失败，请手动保存导出文件')
                  })
              }

              // Trigger file download
              const blob = new Blob([json], { type: 'application/json' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = 'emoji_userscript_export.json'
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(url)
            } catch (error) {
              console.error('exportForUserscript failed', error)
              message.error(
                '导出用户脚本数据失败: ' + (error && error.message ? error.message : '')
              )
            }
          },

          getUserscriptPayload() {
            return {
              emojiGroups: this.emojiGroups,
              settings: this.settings,
              favorites: this.favorites
            }
          },

          connectWebSocket() {
            if (!this.wsUrl) {
              message.error('请先填写 WebSocket URL')
              return
            }
            try {
              this.wsInstance = new WebSocket(this.wsUrl)
              this.wsInstance.onopen = () => {
                this.wsConnected = true
                message.success('WebSocket 已连接')
              }
              this.wsInstance.onclose = () => {
                this.wsConnected = false
                this.wsInstance = null
                message.info('WebSocket 已断开')
              }
              this.wsInstance.onerror = err => {
                console.error('WebSocket error', err)
                message.error('WebSocket 错误')
              }
              this.wsInstance.onmessage = ev => {
                try {
                  const msg = JSON.parse(ev.data)
                  // Support a simple request/response protocol
                  if (msg && msg.type === 'request_export') {
                    const payload = this.getUserscriptPayload()
                    const res = { type: 'export', payload }
                    this.wsSend(res)
                    message.success('已通过 WebSocket 发送导出数据')
                  } else {
                    // For debugging: show incoming messages in console
                    console.debug('WS message received', msg)
                  }
                } catch (e) {
                  console.warn('Failed to parse WS message', ev.data)
                }
              }
            } catch (error) {
              console.error('connectWebSocket failed', error)
              message.error('连接 WebSocket 失败: ' + (error && error.message ? error.message : ''))
            }
          },

          disconnectWebSocket() {
            try {
              if (this.wsInstance) {
                this.wsInstance.close()
                this.wsInstance = null
              }
              this.wsConnected = false
            } catch (error) {
              console.error('disconnectWebSocket failed', error)
            }
          },

          wsSend(obj) {
            try {
              if (!this.wsInstance || this.wsInstance.readyState !== WebSocket.OPEN) {
                message.error('WebSocket 未连接')
                return
              }
              const text = typeof obj === 'string' ? obj : JSON.stringify(obj)
              this.wsInstance.send(text)
            } catch (error) {
              console.error('wsSend failed', error)
            }
          },

          syncToUserscript() {
            try {
              // Save to userscript localStorage keys
              localStorage.setItem(
                'emoji_extension_userscript_data',
                JSON.stringify(this.emojiGroups)
              )
              localStorage.setItem(
                'emoji_extension_userscript_settings',
                JSON.stringify(this.settings)
              )
              message.success('数据已同步到用户脚本')
            } catch (error) {
              message.error('同步失败: ' + error.message)
            }
          },

          performImport() {
            try {
              const data = JSON.parse(this.importData)

              if (this.importFormat === 'userscript') {
                if (data.emojiGroups) {
                  this.emojiGroups = data.emojiGroups
                }
                if (data.settings) {
                  this.settings = { ...this.settings, ...data.settings }
                }
              } else {
                if (data.groups) {
                  this.emojiGroups = data.groups
                }
                if (data.settings) {
                  this.settings = { ...this.settings, ...data.settings }
                }
                if (data.favorites) {
                  this.favorites = data.favorites
                }
              }

              this.saveData()
              this.openImportModalFlag = false
              this.importData = ''
              message.success('数据导入成功')
            } catch (error) {
              message.error('导入失败，请检查数据格式')
            }
          },

          handleImageError(event) {
            event.target.style.display = 'none'
          }
        }
      }).mount('#app')
    </script>
  </body>
</html>
