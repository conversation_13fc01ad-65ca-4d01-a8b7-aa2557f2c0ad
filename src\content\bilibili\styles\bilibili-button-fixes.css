/**
 * Bilibili Button Styling Fixes
 * 
 * This CSS ensures that injected buttons integrate seamlessly with
 * Bilibili's native UI elements by removing conflicting styles and
 * allowing the site's original CSS to take precedence.
 */

/* 
 * Fix for control buttons in image viewer
 * Remove any conflicting inline styles and ensure proper inheritance
 */
.bili-album__watch__control__option.add-emoji {
  /* Reset any problematic properties that might conflict */
  background: inherit !important;
  color: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  padding: inherit !important;
  border-radius: inherit !important;
  transition: inherit !important;
  
  /* Ensure proper display and alignment */
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  
  /* Ensure clickability */
  cursor: pointer !important;
  user-select: none !important;
}

/* 
 * Ensure the button inherits hover states from Bilibili's CSS
 * Remove any custom hover effects that might conflict
 */
.bili-album__watch__control__option.add-emoji:hover {
  /* Let Bilibili's native hover styles take effect */
  background: inherit !important;
  color: inherit !important;
}

/* 
 * Ensure the SVG icon inherits the correct color
 */
.bili-album__watch__control__option.add-emoji svg {
  fill: currentColor !important;
  width: 14px !important;
  height: 14px !important;
}

/* 
 * Ensure text label styling matches other buttons
 */
.bili-album__watch__control__option.add-emoji span {
  color: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

/* 
 * PhotoSwipe button styling fixes
 */
.pswp__button.bili-emoji-add-btn {
  /* Ensure proper PhotoSwipe button appearance */
  position: relative !important;
  display: block !important;
  width: 44px !important;
  height: 44px !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  overflow: visible !important;
  appearance: none !important;
  box-shadow: none !important;
  opacity: 0.75 !important;
  transition: opacity 0.2s !important;
  color: #fff !important;
  font-size: 18px !important;
  line-height: 44px !important;
  text-align: center !important;
}

.pswp__button.bili-emoji-add-btn:hover {
  opacity: 1 !important;
}

/* 
 * Floating button styling (for non-control areas)
 */
.bili-emoji-add-btn {
  /* Ensure floating buttons don't interfere with page layout */
  position: absolute !important;
  z-index: 9999 !important;
  cursor: pointer !important;
  border-radius: 6px !important;
  padding: 6px 8px !important;
  background: rgba(0, 0, 0, 0.6) !important;
  color: #fff !important;
  border: none !important;
  font-weight: 700 !important;
  font-size: 14px !important;
  line-height: 1 !important;
  
  /* Smooth transitions */
  transition: all 0.2s ease !important;
}

.bili-emoji-add-btn:hover {
  background: rgba(0, 0, 0, 0.8) !important;
  transform: scale(1.05) !important;
}

/* 
 * Success/failure state styling
 */
.bili-emoji-add-btn.success {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

.bili-emoji-add-btn.error {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

/* 
 * Batch parse button styling
 */
.bili-emoji-batch-parse {
  display: inline-flex !important;
  align-items: center !important;
  gap: 6px !important;
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
  color: #fff !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  margin: 8px 0 !important;
  font-weight: 600 !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.bili-emoji-batch-parse:hover {
  background: linear-gradient(135deg, #d97706, #b45309) !important;
  transform: translateY(-1px) !important;
}

.bili-emoji-batch-parse:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* 
 * Responsive adjustments for mobile
 */
@media (max-width: 768px) {
  .bili-album__watch__control__option.add-emoji {
    font-size: 11px !important;
    padding: 6px 8px !important;
    gap: 3px !important;
  }
  
  .bili-album__watch__control__option.add-emoji svg {
    width: 12px !important;
    height: 12px !important;
  }
  
  .pswp__button.bili-emoji-add-btn {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
    line-height: 40px !important;
  }
}

/* 
 * High contrast mode support
 */
@media (prefers-contrast: high) {
  .bili-emoji-add-btn {
    border: 2px solid currentColor !important;
  }
  
  .bili-album__watch__control__option.add-emoji {
    border: 1px solid currentColor !important;
  }
}

/* 
 * Reduced motion support
 */
@media (prefers-reduced-motion: reduce) {
  .bili-emoji-add-btn,
  .bili-album__watch__control__option.add-emoji,
  .bili-emoji-batch-parse {
    transition: none !important;
    transform: none !important;
  }
}

/* 
 * Dark mode adjustments (if Bilibili implements dark mode)
 */
@media (prefers-color-scheme: dark) {
  .bili-emoji-add-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
  }
  
  .bili-emoji-add-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
  }
}
