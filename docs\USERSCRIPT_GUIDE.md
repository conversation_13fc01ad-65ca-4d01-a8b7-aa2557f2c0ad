# 油猴脚本使用指南

## 🚀 功能概览

油猴脚本版本现在提供与浏览器扩展版本相同的完整功能！包括：

### 🐈‍⬛ 核心功能

- **表情选择器**：点击工具栏的猫咪图标打开
- **搜索功能**：实时搜索过滤表情
- **一键添加**：在图片弹窗中直接添加表情到收藏
- **智能插入**：支持 Markdown 和 HTML 格式
- **本地存储**：所有数据保存在浏览器本地

### ⚙️ 管理功能

- **设置面板**：点击 🔧 图标配置缩放、格式等
- **数据管理**：点击 ⚙️ 图标进行导入/导出操作
- **完整管理界面**：独立的 HTML 管理器，提供扩展级别的功能

## 📦 文件说明

### 用户脚本文件

- `emoji-extension.user.js` - 标准版本 (45KB，包含调试信息)
- `emoji-extension-min.user.js` - 压缩版本 (37KB，优化性能)

### 管理界面

- `emoji-manager.html` - 完整的表情管理界面

## 🔧 安装使用

### 1. 安装用户脚本

1. 安装 Tampermonkey 或 Greasemonkey 扩展
2. 点击用户脚本文件直接安装
3. 或复制脚本内容到用户脚本管理器

### 2. 使用管理界面（可选）

1. 下载 `emoji-manager.html` 文件
2. 在浏览器中打开该文件
3. 在管理界面中编辑表情和设置
4. 点击"同步到用户脚本"应用更改

## 🎯 使用方法

### 基础使用

1. 在支持的论坛（如 linux.do）中找到编辑器工具栏
2. 点击 🐈‍⬛ 图标打开表情选择器
3. 点击表情即可插入到编辑器中

### 高级设置

1. 在表情选择器中点击 🔧 设置图标
2. 调整图片缩放比例（5%-150%）
3. 选择输出格式（Markdown/HTML）
4. 控制搜索栏显示

### 数据管理

1. 在表情选择器中点击 ⚙️ 管理图标
2. 选择导出数据、导入数据或同步功能
3. 使用完整管理界面进行高级编辑

## 🔄 数据同步

### 用户脚本 → 管理界面

1. 在用户脚本中点击"导出数据"
2. 在管理界面中选择"导入数据"
3. 粘贴导出的 JSON 数据

### 管理界面 → 用户脚本

1. 在管理界面中编辑表情和设置
2. 点击"同步到用户脚本"按钮
3. 刷新论坛页面查看更改

## 🌐 支持的网站

- **linux.do** - Linux 中文社区
- **meta.discourse.org** - Discourse 官方
- **所有 Discourse 平台** - 自动检测

## ⭐ 功能对比

| 功能          | 用户脚本           | Chrome 扩展 |
| ------------- | ------------------ | ----------- |
| 表情选择器    | ✅                 | ✅          |
| 搜索功能      | ✅                 | ✅          |
| 一键添加      | ✅                 | ✅          |
| 设置管理      | ✅                 | ✅          |
| 分组管理      | ✅（通过管理界面） | ✅          |
| 拖拽排序      | ✅（通过管理界面） | ✅          |
| 数据导入/导出 | ✅                 | ✅          |
| 收藏夹        | ✅（通过管理界面） | ✅          |
| 后台同步      | ❌                 | ✅          |
| 右键菜单      | ❌                 | ✅          |

## 🚨 注意事项

1. **数据安全**：所有数据保存在浏览器本地，请定期导出备份
2. **兼容性**：建议使用最新版本的 Tampermonkey
3. **性能**：推荐使用压缩版本以获得更好的加载性能
4. **更新**：脚本会自动检查更新，也可手动重新安装

## 🔧 故障排除

### 表情选择器不显示

1. 检查是否在支持的网站上
2. 确认页面已完全加载
3. 刷新页面重试

### 数据丢失

1. 检查浏览器存储权限
2. 尝试从备份文件恢复
3. 重新安装脚本

### 同步失败

1. 确保管理界面和用户脚本在同一域名下
2. 检查浏览器存储限制
3. 尝试手动导入/导出数据

## 📞 技术支持

- **GitHub Issues**：[提交问题](https://github.com/stevessr/bug-v3/issues)
- **讨论区**：[参与讨论](https://linux.do)
- **更新日志**：查看 GitHub Releases

---

**版本**: v1.0.0  
**更新时间**: 2024-12-27  
**兼容性**: Tampermonkey 4.0+, Greasemonkey 4.0+
